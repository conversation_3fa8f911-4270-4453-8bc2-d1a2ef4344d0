import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';

/// 分类导航栏状态管理
final categoryNavigationProvider =
    StateNotifierProvider<CategoryNavigationNotifier, CategoryNavigationState>(
        (ref) {
  return CategoryNavigationNotifier();
});

class CategoryNavigationState {
  final int selectedIndex;
  final bool isExpanded;

  CategoryNavigationState({
    this.selectedIndex = 0,
    this.isExpanded = true,
  });

  CategoryNavigationState copyWith({
    int? selectedIndex,
    bool? isExpanded,
  }) {
    return CategoryNavigationState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

class CategoryNavigationNotifier
    extends StateNotifier<CategoryNavigationState> {
  CategoryNavigationNotifier() : super(CategoryNavigationState());

  void selectCategory(int index) {
    state = state.copyWith(selectedIndex: index);
  }

  void toggleExpansion() {
    state = state.copyWith(isExpanded: !state.isExpanded);
  }

  void initializeWithIndex(int index) {
    state = CategoryNavigationState(
      selectedIndex: index,
      isExpanded: state.isExpanded,
    );
  }
}

class PhotoPortraitCategoryPage extends ConsumerWidget {
  const PhotoPortraitCategoryPage({
    super.key,
    this.categoryId = 0,
  });

  /// 分类ID
  final int categoryId;

  // 模拟数据 - 写真分类数据（与主页面保持一致）
  List<PhotoPortraitCategory> get mockCategoryData => [
        PhotoPortraitCategory()
          ..caseName = "热门"
          ..id = 1
          ..sort = 1
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=1"
              ..caseTitle = "热门写真1"
              ..casePrompt = "时尚热门写真风格"
              ..caseId = 101
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/350?random=2"
              ..caseTitle = "热门写真2"
              ..casePrompt = "流行写真风格"
              ..caseId = 102
              ..caseWidth = 200
              ..caseHeight = 350,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=3"
              ..caseTitle = "热门写真3"
              ..casePrompt = "经典写真风格"
              ..caseId = 103
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=4"
              ..caseTitle = "热门写真4"
              ..casePrompt = "现代写真风格"
              ..caseId = 104
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=17"
              ..caseTitle = "热门写真5"
              ..casePrompt = "优雅写真风格"
              ..caseId = 105
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=18"
              ..caseTitle = "热门写真6"
              ..casePrompt = "清新写真风格"
              ..caseId = 106
              ..caseWidth = 200
              ..caseHeight = 310,
          ],
        PhotoPortraitCategory()
          ..caseName = "双重曝光"
          ..id = 2
          ..sort = 2
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=5"
              ..caseTitle = "双重曝光1"
              ..casePrompt = "艺术双重曝光效果"
              ..caseId = 201
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=6"
              ..caseTitle = "双重曝光2"
              ..casePrompt = "创意双重曝光"
              ..caseId = 202
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=7"
              ..caseTitle = "双重曝光3"
              ..casePrompt = "梦幻双重曝光"
              ..caseId = 203
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/340?random=8"
              ..caseTitle = "双重曝光4"
              ..casePrompt = "专业双重曝光"
              ..caseId = 204
              ..caseWidth = 200
              ..caseHeight = 340,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=19"
              ..caseTitle = "双重曝光5"
              ..casePrompt = "唯美双重曝光"
              ..caseId = 205
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
        PhotoPortraitCategory()
          ..caseName = "网感大片"
          ..id = 3
          ..sort = 3
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=9"
              ..caseTitle = "网感大片1"
              ..casePrompt = "时尚网感风格"
              ..caseId = 301
              ..caseWidth = 200
              ..caseHeight = 300,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/280?random=10"
              ..caseTitle = "网感大片2"
              ..casePrompt = "潮流网感风格"
              ..caseId = 302
              ..caseWidth = 200
              ..caseHeight = 280,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/320?random=11"
              ..caseTitle = "网感大片3"
              ..casePrompt = "个性网感风格"
              ..caseId = 303
              ..caseWidth = 200
              ..caseHeight = 320,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/290?random=12"
              ..caseTitle = "网感大片4"
              ..casePrompt = "酷炫网感风格"
              ..caseId = 304
              ..caseWidth = 200
              ..caseHeight = 290,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=20"
              ..caseTitle = "网感大片5"
              ..casePrompt = "前卫网感风格"
              ..caseId = 305
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=21"
              ..caseTitle = "网感大片6"
              ..casePrompt = "炫酷网感风格"
              ..caseId = 306
              ..caseWidth = 200
              ..caseHeight = 270,
          ],
        PhotoPortraitCategory()
          ..caseName = "宠物写真"
          ..id = 4
          ..sort = 4
          ..state = 1
          ..details = [
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/310?random=13"
              ..caseTitle = "宠物写真1"
              ..casePrompt = "可爱宠物风格"
              ..caseId = 401
              ..caseWidth = 200
              ..caseHeight = 310,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/270?random=14"
              ..caseTitle = "宠物写真2"
              ..casePrompt = "萌宠写真风格"
              ..caseId = 402
              ..caseWidth = 200
              ..caseHeight = 270,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/330?random=15"
              ..caseTitle = "宠物写真3"
              ..casePrompt = "温馨宠物风格"
              ..caseId = 403
              ..caseWidth = 200
              ..caseHeight = 330,
            PhotoPortraitCategoryDetail()
              ..caseImage = "https://picsum.photos/200/300?random=16"
              ..caseTitle = "宠物写真4"
              ..casePrompt = "活泼宠物风格"
              ..caseId = 404
              ..caseWidth = 200
              ..caseHeight = 300,
          ],
      ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final navigationState = ref.watch(categoryNavigationProvider);
    final categories = mockCategoryData;

    // 初始化时设置正确的分类索引
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (navigationState.selectedIndex != categoryId &&
          categoryId >= 0 &&
          categoryId < categories.length) {
        ref
            .read(categoryNavigationProvider.notifier)
            .selectCategory(categoryId);
      }
    });

    final selectedCategory = categories.isNotEmpty &&
            navigationState.selectedIndex < categories.length
        ? categories[navigationState.selectedIndex]
        : null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          selectedCategory?.caseName ?? "写真分类",
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: Leading(
          color: Colors.white,
          onBack: () {
            // 点击leading按钮时切换导航栏展开/收起状态
            ref.read(categoryNavigationProvider.notifier).toggleExpansion();
          },
        ),
        actions: [
          InkWell(
            onTap: () => _handleGenerationRecord(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // 分类导航栏
          _buildCategoryNavigation(context, ref, categories, navigationState),
          // 分类内容
          Expanded(
            child: selectedCategory != null
                ? _buildCategoryContent(context, selectedCategory)
                : const Center(
                    child: Text(
                      "暂无分类数据",
                      style: TextStyle(color: Colors.white54),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建分类导航栏
  Widget _buildCategoryNavigation(
    BuildContext context,
    WidgetRef ref,
    List<PhotoPortraitCategory> categories,
    CategoryNavigationState navigationState,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: navigationState.isExpanded ? 60 : 40,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        separatorBuilder: (context, index) => const SizedBox(width: 16),
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = navigationState.selectedIndex == index;

          return _buildCategoryTab(
            context,
            ref,
            category,
            index,
            isSelected,
            navigationState.isExpanded,
          );
        },
      ),
    );
  }

  /// 构建单个分类标签
  Widget _buildCategoryTab(
    BuildContext context,
    WidgetRef ref,
    PhotoPortraitCategory category,
    int index,
    bool isSelected,
    bool isExpanded,
  ) {
    return InkWell(
      onTap: () {
        ref.read(categoryNavigationProvider.notifier).selectCategory(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: isExpanded ? 16 : 12,
          vertical: isExpanded ? 8 : 6,
        ),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF4A90E2), Color(0xFF357ABD)],
                )
              : null,
          color: isSelected ? null : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            category.caseName ?? "",
            style: TextStyle(
              fontSize: isExpanded ? 14 : 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建分类内容
  Widget _buildCategoryContent(
      BuildContext context, PhotoPortraitCategory category) {
    final details = category.details ?? [];

    if (details.isEmpty) {
      return const Center(
        child: Text(
          "该分类暂无内容",
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: details.length,
      itemBuilder: (context, index) {
        final detail = details[index];
        return _buildCategoryDetailItem(context, detail);
      },
    );
  }

  /// 构建分类详情项
  Widget _buildCategoryDetailItem(
      BuildContext context, PhotoPortraitCategoryDetail detail) {
    return InkWell(
      onTap: () => _handleDetailItemTap(detail),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 网络图片
              CachedNetworkImage(
                imageUrl: detail.caseImage ?? "",
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.purple.withValues(alpha: 0.6),
                        Colors.pink.withValues(alpha: 0.8),
                        Colors.orange.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.grey.withValues(alpha: 0.6),
                        Colors.grey.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 32,
                    ),
                  ),
                ),
              ),
              // 渐变遮罩
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.6),
                    ],
                  ),
                ),
              ),
              // 标题和描述
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (detail.caseTitle?.isNotEmpty == true)
                        Text(
                          detail.caseTitle!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      if (detail.casePrompt?.isNotEmpty == true) ...[
                        const SizedBox(height: 4),
                        Text(
                          detail.casePrompt!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white70,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理生成记录按钮点击
  void _handleGenerationRecord() {
    // TODO: 跳转到生成记录页面
    debugPrint("跳转到生成记录页面");
  }

  /// 处理详情项点击
  void _handleDetailItemTap(PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}，ID: ${detail.caseId}");
  }
}
